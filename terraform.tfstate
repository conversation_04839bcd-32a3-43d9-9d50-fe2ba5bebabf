{"version": 4, "terraform_version": "1.5.3", "serial": 10, "lineage": "a69e6f79-5543-30c6-b8d1-2815202c3ec3", "outputs": {"jwt_secret_arn": {"value": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0", "type": "string", "sensitive": true}}, "resources": [{"mode": "managed", "type": "aws_api_gateway_authorizer", "name": "lambda", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"mode": "managed", "type": "aws_api_gateway_rest_api", "name": "main", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"api_key_source": "HEADER", "arn": "arn:aws:apigateway:us-east-1::/restapis/dg1tac6z1c", "binary_media_types": [], "body": null, "created_date": "2025-08-29T08:18:17Z", "description": "Notification Platform API Gateway", "disable_execute_api_endpoint": false, "endpoint_configuration": [{"ip_address_type": "ipv4", "types": ["EDGE"], "vpc_endpoint_ids": []}], "execution_arn": "arn:aws:execute-api:us-east-1:975391191374:dg1tac6z1c", "fail_on_warnings": null, "id": "dg1tac6z1c", "minimum_compression_size": "", "name": "YANTECH-YNP01-AWS-APIGateway-Dev", "parameters": null, "policy": "", "put_rest_api_mode": null, "region": "us-east-1", "root_resource_id": "xbeoz3o9da", "tags": null, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_dynamodb_table", "name": "api_keys", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-east-1:975391191374:table/YANTECH-YNP01-AWS-DynamoDB-APIKeys-Dev", "attribute": [{"name": "api_key_hash", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "api_key_hash", "id": "YANTECH-YNP01-AWS-DynamoDB-APIKeys-Dev", "import_table": [], "local_secondary_index": [], "name": "YANTECH-YNP01-AWS-DynamoDB-APIKeys-Dev", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": false, "recovery_period_in_days": 0}], "range_key": null, "read_capacity": 0, "region": "us-east-1", "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Environment": "<PERSON>", "Project": "YNP01"}, "tags_all": {"Environment": "<PERSON>", "Project": "YNP01"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::975391191374:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-Dev", "attachment_count": 0, "description": "Permissions for Lambda Authorizer", "id": "arn:aws:iam::975391191374:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-Dev", "name": "YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-Dev", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\",\"logs:CreateLogStream\",\"logs:PutLogEvents\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:logs:*:*:*\"},{\"Action\":\"secretsmanager:GetSecretValue\",\"Effect\":\"Allow\",\"Resource\":\"arn:aws:secretsmanager:ap-southeast-1:*:secret:notification-platform/jwt-secret-*\"},{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:UpdateItem\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:us-east-1:975391191374:table/YANTECH-YNP01-AWS-DynamoDB-APIKeys-Dev\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA6GGOYOFHJZGPWSDU5", "tags": null, "tags_all": {}}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.api_keys"]}]}, {"mode": "managed", "type": "aws_iam_role", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::975391191374:role/YANTECH-YNP01-AWS-Lambda-AuthorizerRole-Dev", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeR<PERSON>\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"lambda.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-08-29T08:18:17Z", "description": "", "force_detach_policies": false, "id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-Dev", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-Dev", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": null, "tags_all": {}, "unique_id": "AROA6GGOYOFHA5J2CDGCP"}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "lambda_authorizer", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-Dev/arn:aws:iam::975391191374:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-Dev", "policy_arn": "arn:aws:iam::975391191374:policy/YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-Dev", "role": "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-Dev"}, "sensitive_attributes": [], "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.api_keys", "aws_iam_policy.lambda_authorizer", "aws_iam_role.lambda_authorizer"]}]}, {"mode": "managed", "type": "aws_lambda_permission", "name": "apigw", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"mode": "managed", "type": "aws_secretsmanager_secret", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0", "description": "JWT signing secret for notification platform", "force_overwrite_replica_secret": false, "id": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0", "kms_key_id": "", "name": "notification-platform/jwt-secret-dev", "name_prefix": "", "policy": "", "recovery_window_in_days": 30, "region": "us-east-1", "replica": [], "tags": {"Environment": "dev", "Project": "YNP01"}, "tags_all": {"Environment": "dev", "Project": "YNP01"}}, "sensitive_attributes": [], "private": "bnVsbA=="}]}, {"mode": "managed", "type": "aws_secretsmanager_secret_version", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0", "has_secret_string_wo": null, "id": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0|terraform-20250829081818094600000002", "region": "us-east-1", "secret_binary": "", "secret_id": "arn:aws:secretsmanager:us-east-1:975391191374:secret:notification-platform/jwt-secret-dev-EfNVV0", "secret_string": "{\"jwt_secret\":\"GFYx.0s+)AKxlecgbd;4hi/V0Xr=HM\\u003cob1dU=.fxQn%vEva_#Ac$w:}IV-p]Qyqu\"}", "secret_string_wo": null, "secret_string_wo_version": null, "version_id": "terraform-20250829081818094600000002", "version_stages": ["AWSCURRENT"]}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_string"}]], "private": "bnVsbA==", "dependencies": ["aws_secretsmanager_secret.jwt_secret", "random_password.jwt_secret"]}]}, {"mode": "managed", "type": "random_password", "name": "jwt_secret", "provider": "provider[\"registry.terraform.io/hashicorp/random\"]", "instances": [{"schema_version": 3, "attributes": {"bcrypt_hash": "$2a$10$Q9Q/PW3eMIn0N.M86Xwg4ev9wmb7XbffnaScJUIKRnxOf50Avc1ae", "id": "none", "keepers": null, "length": 64, "lower": true, "min_lower": 0, "min_numeric": 0, "min_special": 0, "min_upper": 0, "number": true, "numeric": true, "override_special": "!@#$%^&*()_-+={}[]|:;<>,.?/~", "result": "GFYx.0s+)AKxlecgbd;4hi/V0Xr=HM<ob1dU=.fxQn%vEva_#Ac$w:}IV-p]Qyqu", "special": true, "upper": true}, "sensitive_attributes": []}]}], "check_results": null}