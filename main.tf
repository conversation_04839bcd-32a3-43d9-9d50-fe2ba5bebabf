

provider "aws" {
  region = var.region 
}

# IAM Role for Lambda Authorizer
resource "aws_iam_role" "lambda_authorizer" {
  name = "YANTECH-YNP01-AWS-Lambda-AuthorizerRole-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      }
    ]
  })
}

# IAM Policy for Lambda Authorizer
resource "aws_iam_policy" "lambda_authorizer" {
  name        = "YANTECH-YNP01-AWS-Lambda-AuthorizerPolicy-${var.environment}"
  description = "Permissions for Lambda Authorizer"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect   = "Allow"
        Action   = "secretsmanager:GetSecretValue"
        Resource = aws_secretsmanager_secret.jwt_secret.arn  # Specific ARN
      },
      {
        Effect = "Allow"
        Action = [
          "dynamodb:GetItem",
          "dynamodb:UpdateItem"
        ]
        Resource = aws_dynamodb_table.api_keys.arn
      }
    ]
  })
}

# Attach policy to role
resource "aws_iam_role_policy_attachment" "lambda_authorizer" {
  role       = aws_iam_role.lambda_authorizer.name
  policy_arn = aws_iam_policy.lambda_authorizer.arn
}

# DynamoDB Table for API Keys
resource "aws_dynamodb_table" "api_keys" {
  name         = "YANTECH-YNP01-AWS-DynamoDB-APIKeys-${var.environment}"
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "api_key_hash"

  attribute {
    name = "api_key_hash"
    type = "S"
  }

  tags = {
    Environment = "${var.environment}"
    Project     = "YNP01"
  }
}

# Lambda Function
resource "aws_lambda_function" "authorizer" {
  filename      = "lambda-authorizer.zip"
  function_name = "YANTECH-YNP01-AWS-Lambda-Authorizer-${var.environment}"
  role          = aws_iam_role.lambda_authorizer.arn
  handler       = "lambda_function.lambda_handler"
  runtime       = "python3.9"
  timeout       = 10

  environment {
    variables = {
      API_KEYS_TABLE  = aws_dynamodb_table.api_keys.name
      JWT_SECRET_NAME = aws_secretsmanager_secret.jwt_secret.name
      TOKEN_CACHE_TTL = "300"
    }
  }
}

# API Gateway
resource "aws_api_gateway_rest_api" "main" {
  name        = "YANTECH-YNP01-AWS-APIGateway-${var.environment}"
  description = "Notification Platform API Gateway"
}

resource "aws_api_gateway_authorizer" "lambda" {
  name          = "YANTECH-YNP01-AWS-APIGateway-Authorizer-${var.environment}"
  rest_api_id   = aws_api_gateway_rest_api.main.id
  authorizer_uri = aws_lambda_function.authorizer.invoke_arn
  identity_source = "method.request.header.Authorization"
  type          = "TOKEN"
}

# Lambda Permission for API Gateway
resource "aws_lambda_permission" "apigw" {
  statement_id  = "AllowAPIGatewayInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.authorizer.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_api_gateway_rest_api.main.execution_arn}/authorizers/${aws_api_gateway_authorizer.lambda.id}"
}

# Secrets Manager (JWT Secret)
resource "aws_secretsmanager_secret" "jwt_secret" {
  name        = "notification-platform/jwt-secret-${var.environment}"
  description = "JWT signing secret for notification platform"

  tags = {
    Project     = "YNP01"
    Environment = var.environment
  }
}

resource "aws_secretsmanager_secret_version" "jwt_secret" {
  secret_id     = aws_secretsmanager_secret.jwt_secret.id
  secret_string = jsonencode({
    jwt_secret = random_password.jwt_secret.result
  })
}

# Generate a secure random password
resource "random_password" "jwt_secret" {
  length           = 64
  special          = true
  override_special = "!@#$%^&*()_-+={}[]|:;<>,.?/~"
}

# Output the secret ARN for reference
output "jwt_secret_arn" {
  value       = aws_secretsmanager_secret.jwt_secret.arn
  description = "ARN of the JWT secret"
  sensitive   = true
}